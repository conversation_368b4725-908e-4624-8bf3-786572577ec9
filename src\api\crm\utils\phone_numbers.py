import phonenumbers


class PhoneNumberUtils:
    """
    Utilidades para manejar números de teléfono nacionales e interancionales
    usando la librería phonenumbers.
    """

    @staticmethod
    def is_phone_number(value, strict=False):
        """
        Determina si un número o texto es un número de teléfono nacional o internacional.

        Args:
            value (str): El valor a verificar
            strict (bool): Si True, solo acepta números válidos. Si False, acepta números posibles.

        Returns:
            bool: True si es un número de teléfono válido/posible, False en caso contrario
        """
        if not value:
            return False

        # Basic cleaning for initial check
        cleaned = str(value).strip()
        cleaned = cleaned.replace("(", "").replace(")", "").replace("-", "")
        cleaned = cleaned.replace(" ", "")

        # Quick check: should be mostly digits and reasonable length (keep + for international)
        test_cleaned = cleaned.replace("+", "")
        if not (6 <= len(test_cleaned) and test_cleaned.isdigit()):
            return False

        # Test with original value first
        for test_value in [value, cleaned]:
            try:
                # Try parsing as international number first
                parsed = phonenumbers.parse(test_value, None)
                if strict:
                    if phonenumbers.is_valid_number(parsed):
                        return True
                else:
                    if phonenumbers.is_possible_number(parsed):
                        return True
            except phonenumbers.NumberParseException:
                pass

            try:
                # Try parsing with Peru as default country
                parsed = phonenumbers.parse(test_value, "PE")
                if strict:
                    if phonenumbers.is_valid_number(parsed):
                        return True
                else:
                    if phonenumbers.is_possible_number(parsed):
                        return True
            except phonenumbers.NumberParseException:
                pass

        return False

    @staticmethod
    def normalize_phone_number(phone_number, default_country="PE", strict=False):
        """
        Normaliza un número de teléfono a diferentes formatos.

        Args:
            phone_number (str): El número a normalizar
            default_country (str): País por defecto para números nacionales
            strict (bool): Si True, solo acepta números válidos. Si False, acepta números posibles.

        Returns:
            dict: Información normalizada del número
        """
        if not phone_number:
            return None

        # Basic cleaning
        cleaned = str(phone_number).strip()
        cleaned = cleaned.replace("(", "").replace(")", "").replace("-", "")
        cleaned = cleaned.replace(" ", "")

        result = {
            "original": phone_number,
            "cleaned": cleaned.replace("+", ""),
            "e164": None,
            "national": None,
            "international": None,
            "country_code": None,
            "is_valid": False,
            "is_possible": False,
        }

        def process_parsed_number(parsed):
            """Helper function to process a parsed number"""
            is_valid = phonenumbers.is_valid_number(parsed)
            is_possible = phonenumbers.is_possible_number(parsed)

            # Accept if valid, or if possible and not in strict mode
            if is_valid or (is_possible and not strict):
                result["e164"] = phonenumbers.format_number(
                    parsed, phonenumbers.PhoneNumberFormat.E164
                )
                result["national"] = phonenumbers.format_number(
                    parsed, phonenumbers.PhoneNumberFormat.NATIONAL
                )
                result["international"] = phonenumbers.format_number(
                    parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL
                )
                result["country_code"] = phonenumbers.region_code_for_number(parsed)
                result["is_valid"] = is_valid
                result["is_possible"] = is_possible
                return True
            return False

        try:
            # Try parsing with default country first
            parsed = phonenumbers.parse(phone_number, default_country)
            if process_parsed_number(parsed):
                return result

        except phonenumbers.NumberParseException:
            pass

        try:
            # Try parsing as international number
            parsed = phonenumbers.parse(phone_number, None)
            if process_parsed_number(parsed):
                return result

        except phonenumbers.NumberParseException:
            pass

        # If parsing fails, return basic info
        return result
