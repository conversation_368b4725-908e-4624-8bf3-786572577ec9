from django_filters import rest_framework as filters
from core.models import Payment
from api.crm.utils.phone_numbers import PhoneNumberUtils
from django.db.models import Q, Value, CharField
from django.db.models.functions import Concat


class CrmPaymentFilter(filters.FilterSet):
    status = filters.CharFilter(method="filter_by_status")
    order = filters.CharFilter(method="filter_by_order")
    owner = filters.CharFilter(method="filter_by_owner")

    search = filters.CharFilter(
        method="filter_search",
        help_text="Filter by order owner's first name, last name, email, or phone number.",
    )

    currency = filters.CharFilter(
        field_name="currency",
        help_text="Filter by payment currency (e.g., usd, eur).",
    )

    start_date = filters.DateFilter(
        method="filter_by_date_range",
        help_text="Start date for filtering (YYYY-MM-DD format). Must be used with end_date and filter_date_by.",
    )

    end_date = filters.DateFilter(
        method="filter_by_date_range",
        help_text="End date for filtering (YYYY-MM-DD format). Must be used with start_date and filter_date_by.",
    )

    filter_date_by = filters.CharFilter(
        method="filter_by_date_range",
        help_text="Comma-separated list of date fields to filter by. "
        "Available options: created_at,payment_date,scheduled_payment_date",
    )

    created_by = filters.CharFilter(
        field_name="created_by",
        help_text="Filter by user who created the payment (UUID).",
    )

    payment_method = filters.CharFilter(
        field_name="payment_method",
        help_text="Filter by payment method (UUID).",
    )

    is_first_payment = filters.BooleanFilter(
        field_name="is_first_payment",
        help_text="Filter by first payment status (true/false).",
    )

    min_amount = filters.NumberFilter(
        method="filter_by_amount_range",
        help_text="Minimum amount for filtering. Can be used alone or with max_amount.",
    )

    max_amount = filters.NumberFilter(
        method="filter_by_amount_range",
        help_text="Maximum amount for filtering. Can be used alone or with min_amount.",
    )

    def filter_by_order(self, queryset, name, value):
        return queryset.filter(order__oid=value)

    def filter_by_status(self, queryset, name, value):
        """
        Permite filtrar por uno o varios estados:
        - status=is_paid
        - status=is_pending (no pagado y no perdido)
        - status=is_lost
        - status=is_paid,is_lost (ambos True)
        """
        if not value:
            return queryset

        filters_kwargs = {}
        for status_key in value.split(","):
            status_key = status_key.strip()
            if status_key == "is_paid":
                filters_kwargs["is_paid"] = True
            elif status_key == "is_lost":
                filters_kwargs["is_lost"] = True
            elif status_key == "is_pending":
                filters_kwargs["is_paid"] = False
                filters_kwargs["is_lost"] = False
                filters_kwargs["is_refund"] = False

        if filters_kwargs:
            return queryset.filter(**filters_kwargs)

        return queryset

    def filter_by_owner(self, queryset, name, value):
        return queryset.filter(order__owner=value)

    def filter_search(self, queryset, name, value):
        """
        Filter by order owner's first name, last name, email, or phone number.
        """
        if not value:
            return queryset

        # Check if the search value looks like a phone number
        if PhoneNumberUtils.is_phone_number(value):
            # Use only the cleaned phone number for search
            value = PhoneNumberUtils.normalize_phone_number(value)["cleaned"]
            # filter only for phone number
            return queryset.filter(order__owner__phone_number__icontains=value)

        queryset_with_full_name = queryset.annotate(
            full_name=Concat(
                "order__owner__first_name",
                Value(" "),
                "order__owner__last_name",
                output_field=CharField(),
            )
        )

        return queryset_with_full_name.filter(
            Q(order__owner__first_name__icontains=value)
            | Q(order__owner__last_name__icontains=value)
            | Q(order__owner__email__icontains=value)
            | Q(order__owner__phone_number__icontains=value)
            | Q(full_name__icontains=value)  # Search in concatenated full name
        )

    def filter_by_date_range(self, queryset, name, value):
        """
        Filter payments by date range for specific date fields.
        Requires start_date, end_date, and filter_date_by parameters.

        Example: ?start_date=2025-08-05&end_date=2025-08-06&filter_date_by=created_at,payment_date
        """
        # Get all filter parameters from the request
        params = self.request.GET
        start_date = params.get("start_date")
        end_date = params.get("end_date")
        filter_date_by = params.get("filter_date_by")

        # All three parameters are required for date filtering
        if not all([start_date, end_date, filter_date_by]):
            return queryset

        # Parse the date fields to filter by
        date_fields = [
            field.strip() for field in filter_date_by.split(",") if field.strip()
        ]

        # Valid Django model field names
        valid_field_names = {
            "created_at",
            "payment_date",
            "scheduled_payment_date",
        }

        # Validate field names
        valid_fields = []
        for field in date_fields:
            if field in valid_field_names:
                valid_fields.append(field)

        if not valid_fields:
            return queryset

        # Build the filter condition using OR logic
        date_filter = Q()
        for field in valid_fields:
            # Create date range filter for each field
            field_filter = Q(
                **{
                    f"{field}__date__gte": start_date,
                    f"{field}__date__lte": end_date,
                }
            )
            # Combine with OR logic (payments matching ANY of the date fields)
            date_filter |= field_filter

        return queryset.filter(date_filter)

    def filter_by_amount_range(self, queryset, name, value):
        """
        Filter payments by amount range.
        Can use min_amount, max_amount, or both parameters.

        Examples:
        - ?min_amount=100 (amount >= 100)
        - ?max_amount=500 (amount <= 500)
        - ?min_amount=100&max_amount=500 (100 <= amount <= 500)
        """
        # Get all filter parameters from the request
        params = self.request.GET
        min_amount = params.get("min_amount")
        max_amount = params.get("max_amount")

        # At least one parameter is required for amount filtering
        if not any([min_amount, max_amount]):
            return queryset

        filter_kwargs = {}

        try:
            if min_amount:
                min_amount = float(min_amount)
                filter_kwargs["amount__gte"] = min_amount

            if max_amount:
                max_amount = float(max_amount)
                filter_kwargs["amount__lte"] = max_amount

            if filter_kwargs:
                return queryset.filter(**filter_kwargs)

        except (ValueError, TypeError):
            return queryset

        return queryset

    class Meta:
        model = Payment
        fields = [
            "status",
            "order",
            "owner",
            "search",
            "currency",
            "start_date",
            "end_date",
            "filter_date_by",
            "created_by",
            "payment_method",
            "is_first_payment",
            "min_amount",
            "max_amount",
        ]
