from django.core.management.base import BaseCommand
from django.contrib.contenttypes.models import ContentType
from core.models.template import TemplateVariable, TemplateType
from core.models.event import EventScheduleEnrollment


class Command(BaseCommand):
    help = "Populate template variables with common variables for different contexts"

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS("Starting to populate template variables...")
        )

        # TODO: Activity reminders using a polymorphic model Reminders
        # activity_ct = ContentType.objects.get_for_model(Activity)

        # Get ContentTypes
        event_enrollment_ct = ContentType.objects.get_for_model(EventScheduleEnrollment)

        # Create or get TemplateTypes
        event_enrollment_tt, _ = TemplateType.objects.get_or_create(
            content_type=event_enrollment_ct,
            defaults={"name": "Recordatorio de Eventos"},
        )

        # activity_tt, _ = TemplateType.objects.get_or_create(
        #     content_type=activity_ct,
        #     defaults={
        #         'name': 'Actividades comerciales',
        #         'is_active': True
        #     }
        # )

        # Event Reminder Variables
        event_reminder_variables = [
            {
                "name": "nombre",
                "path": "first_name",
                "example": "<PERSON><PERSON>",
                "description": "Nombre del usuario inscrito en el evento",
            },
            {
                "name": "apellido",
                "path": "last_name",
                "example": "Inti Lobato",
                "description": "Apellido del usuario inscrito en el evento",
            },
            {
                "name": "correo",
                "path": "email",
                "example": "<EMAIL>",
                "description": "Coorreo electrónico gmail del usuario inscrito en el evento",
            },
            {
                "name": "universidad",
                "path": "university",
                "example": "Universidad Nacional de Ingeniería",
                "description": "Universidad a la que pertenece el usuario inscrito en el evento",
            },
            {
                "name": "intereses de oferta",
                "path": "interests",
                "example": '["PE Finanzas Avanzadas", "PE Ciencia de Datos]',
                "description": "Lista de intereses de oferta",
                "data_type": TemplateVariable.ARRAY,
            },
            {
                "name": "instructor",
                "path": "event_schedule__instructor__full_name",
                "example": "Gerardo Inti Lobato",
                "description": "Nombre completo del instructor del evento",
            },
            {
                "name": "titulo de instructor",
                "path": "event_schedule__instructor__title",
                "example": "Msc(c) Econometría Bancaria y Financiera",
                "description": "Título del instructor del evento",
            },
            {
                "name": "meet link",
                "path": "event_schedule__ext_event_link",
                "example": "https://meet.google.com/abc-def-ghi",
                "description": "Link para acceder al evento virtual",
            },
            {
                "name": "hora de inicio",
                "path": "event_schedule__start_date",
                "example": "10:00 am",
                "description": "Hora de inicio del evento",
                "data_type": TemplateVariable.DATETIME,
                "data_format": TemplateVariable.TIME_12H,
            },
            {
                "name": "fecha de inicio",
                "path": "event_schedule__start_date",
                "example": "Viernes, 11 de julio",
                "description": "Fecha de inicio del evento",
                "data_type": TemplateVariable.DATETIME,
                "data_format": TemplateVariable.DATE_WEEKDAY_MEDIUM,
            },
            {
                "name": "evento",
                "path": "event_schedule__name",
                "example": "Taller: Programa de Especialización en Finanzas Avanzadas",
                "description": "Nombre del evento",
            },
        ]

        # Activity Variables
        # activity_variables = []

        # Create Event Reminder variables
        self.create_variables(
            event_reminder_variables, event_enrollment_tt, "Event Reminder"
        )

        # Create Activity variables
        # self.create_variables(activity_variables, activity_tt, 'Activity')

        self.stdout.write(
            self.style.SUCCESS("Template variables populated successfully!")
        )

    def create_variables(self, variables_data, template_type, context_name):
        """Create template variables for a specific template type"""
        created_count = 0
        updated_count = 0

        for var_data in variables_data:
            defaults = {
                "path": var_data["path"],
                "example": var_data["example"],
                "description": var_data["description"],
                "data_type": var_data.get("data_type", TemplateVariable.STRING),
                "data_format": var_data.get("data_format"),
            }

            template_var, created = TemplateVariable.objects.get_or_create(
                name=var_data["name"],
                template_type=template_type,
                defaults=defaults,
            )

            if created:
                created_count += 1
                self.stdout.write(f"  ✓ Created: {var_data['name']}")
            else:
                # Update existing variable
                template_var.path = var_data["path"]
                template_var.example = var_data["example"]
                template_var.description = var_data["description"]
                template_var.data_type = var_data.get(
                    "data_type", TemplateVariable.STRING
                )
                template_var.data_format = var_data.get("data_format")
                template_var.save()
                updated_count += 1
                self.stdout.write(f"  ↻ Updated: {var_data['name']}")

        self.stdout.write(
            self.style.SUCCESS(
                f"{context_name}: {created_count} created, {updated_count} updated"
            )
        )
