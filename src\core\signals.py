import logging
from django.dispatch import receiver
from django.db.models.signals import post_delete, post_migrate
from core.models import Instructor, File
from core.utils import delete_file_from_bucket

logger = logging.getLogger(__name__)


@receiver(post_delete, sender=Instructor)
def delete_instructor_profile_photo(sender, instance: Instructor, **kwargs):
    if instance.profile_photo:
        instance.profile_photo.delete()


@receiver(post_delete, sender=File)
def delete_file(sender, instance: File, **kwargs):
    delete_file_from_bucket(instance.bucket_name, instance.object_name)


@receiver(post_migrate)
def auto_execute_seeds(sender, **kwargs):
    """
    Automatically execute essential seeds after migrations
    This ensures that critical data is populated when the system starts
    """
    # Only execute for core app migrations to avoid multiple executions
    if sender.name != "core":
        return

    logger.info("Post-migrate signal triggered, executing template variables seed...")

    # Import here to avoid circular imports
    try:
        from src.api.crm.services.seed.template_variable import (
            populate_template_variable_data,
        )

        # Execute template variables seed directly with get_or_create logic
        created_count, updated_count = populate_template_variable_data()

        logger.info(
            f"Template variables seed completed. "
            f"Created: {created_count}, Updated: {updated_count}"
        )

    except Exception as e:
        logger.error(f"Error executing template variables seed: {str(e)}")
        # Don't raise the exception to avoid breaking migrations
