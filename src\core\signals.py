import logging
from django.dispatch import receiver
from django.db.models.signals import post_delete, post_migrate
from django.apps import apps
from core.models import Instructor, File
from core.utils import delete_file_from_bucket
from core.services import SeedManager

logger = logging.getLogger(__name__)


@receiver(post_delete, sender=Instructor)
def delete_instructor_profile_photo(sender, instance: Instructor, **kwargs):
    if instance.profile_photo:
        instance.profile_photo.delete()


@receiver(post_delete, sender=File)
def delete_file(sender, instance: File, **kwargs):
    delete_file_from_bucket(instance.bucket_name, instance.object_name)


@receiver(post_migrate)
def auto_execute_seeds(sender, **kwargs):
    """
    Automatically execute essential seeds after migrations
    This ensures that critical data is populated when the system starts
    """
    # Only execute for core app migrations to avoid multiple executions
    if sender.name != 'core':
        return

    logger.info("Post-migrate signal triggered, checking for pending seeds...")

    # Import here to avoid circular imports
    try:
        from api.crm.services.populate.template_variable import populate_template_variable_data

        # Execute template variables seed
        result = SeedManager.execute_seed(
            seed_name="template_variables",
            populate_function=populate_template_variable_data,
            version="1.0.0"
        )

        if result["executed"]:
            if result["success"]:
                logger.info(
                    f"Template variables seed executed successfully. "
                    f"Created: {result.get('records_created', 0)}, "
                    f"Updated: {result.get('records_updated', 0)}"
                )
            else:
                logger.error(f"Template variables seed failed: {result.get('error', 'Unknown error')}")
        else:
            logger.info("Template variables seed skipped (already executed)")

    except Exception as e:
        logger.error(f"Error executing auto seeds: {str(e)}")
        # Don't raise the exception to avoid breaking migrations
