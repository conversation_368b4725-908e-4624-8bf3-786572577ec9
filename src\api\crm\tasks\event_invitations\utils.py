"""
Utility functions for event invitation tasks
"""

import logging
import random

logger = logging.getLogger(__name__)


def get_random_delay(min_delay, max_delay):
    """
    Generate a random delay between event schedule min and max seconds to prevent Meta bans

    Args:
        min_delay: Minimum delay in seconds
        max_delay: Maximum delay in seconds

    Returns:
        int: Random delay in seconds between min and max
    """
    # Validate inputs and provide fallback values
    if min_delay is None or max_delay is None:
        logger.warning(
            "WhatsApp delay range not configured, using default 5-15 seconds"
        )
        min_delay, max_delay = 5, 15

    # Ensure min is not greater than max
    if min_delay > max_delay:
        min_delay, max_delay = max_delay, min_delay

    # Ensure minimum delay is at least 1 second
    min_delay = max(1, min_delay)
    max_delay = max(1, max_delay)

    delay = random.randint(min_delay, max_delay)
    logger.info(
        f"Generated random delay: {delay} seconds (range: {min_delay}-{max_delay})"
    )
    return delay


def get_whatsapp_delay_range(event_schedule):
    """
    Get WhatsApp delay range from event schedule
    
    Args:
        event_schedule: EventSchedule instance
        
    Returns:
        tuple: (min_delay, max_delay) or (None, None) if not configured
    """
    delay_range = event_schedule.whatsapp_delay_range
    if delay_range:
        return delay_range.lower, delay_range.upper
    return None, None


def should_use_delay_for_whatsapp(event_schedule):
    """
    Determine if WhatsApp invitations should use delay based on pending reminders count
    
    Args:
        event_schedule: EventSchedule instance
        
    Returns:
        bool: True if delay should be used
    """
    from core.models import EventReminder
    
    pending_reminders_count = EventReminder.objects.filter(
        enrollment__event_schedule=event_schedule,
        status_whatsapp=EventReminder.PENDING,
        deleted=False,
    ).count()
    
    return pending_reminders_count > 1
