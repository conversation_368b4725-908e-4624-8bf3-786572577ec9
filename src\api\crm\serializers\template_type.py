from rest_framework import serializers
from core.models.template import TemplateType


class CrmTemplateTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for TemplateType (for template type selection)
    """

    content_type_id = serializers.IntegerField(source="content_type.id", read_only=True)
    content_type_model = serializers.CharField(
        source="content_type.model", read_only=True
    )

    class Meta:
        model = TemplateType
        fields = ["ttid", "name", "content_type_id", "content_type_model", "is_active"]
        read_only_fields = ["ttid", "content_type_id", "content_type_model"]
