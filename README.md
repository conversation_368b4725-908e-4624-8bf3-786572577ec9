# CEU Portals

This is the README file for the project "CEU Portals". 

## Description

CEU Portals is the core of an integral platform for manage "CEU Centro de Especialización". This repository pretends to be the backend project with Django Rest Framework.

## Installation

1. First you need to allocate the required services like a DB and Bucket. Go to installation directory and deploy locally the docker-compose services. With `make resources` cli command.
2. Then you need to configure an isolate python environment with conda or venv. Then run `make install` command for install the necesary libraries.
3. Then initialize a environment with `make env`.

## Usage

For first usage you need to apply the migrate and create super user commands from django manage script.

You can do this with:

- `make migrate`
- `make sync-permissions` *(create custom permissions)*
- `make create-superuser`

Then for start the development server run:

- `make run-api`

## Custom Permissions Management

This project includes a custom permissions system for granular access control across different modules (CRM, CMS, LMS, ERP).

### Available Commands

```bash
# Sync all custom permissions (run after migrations)
make sync-permissions

# Preview what permissions would be created/updated (dry run)
make sync-permissions-dry-run

# Force sync permissions (update existing ones)
make sync-permissions-force
```

### When to Run Permission Sync

1. **Initial setup**: After first `make migrate`
2. **Manual updates**: When you modify permission definitions in `api/*/permissions.py`
   
### Permission Structure

Custom permissions are defined in each module:
- `api/crm/permissions.py` - CRM module permissions
- `api/cms/permissions.py` - CMS module permissions  
- `api/lms/permissions.py` - LMS module permissions
- `api/erp/permissions.py` - ERP module permissions

Custom permissions must follow the convention:
- **Codename**: `<action>_<module>_<resource>` (e.g., `view_crm_dashboard_contacts`)
- **Name**: `<MODULE> | Can <action> <resource>` (e.g., `CRM | Can view contacts dashboard`)

**Standard Django actions:**
- `view` - View/read access to resources
- `add` - Create new resources  
- `change` - Update existing resources
- `delete` - Delete resources

> **⚠️ Important**: When adding custom actions beyond standard Django actions (`view`, `add`, `change`, `delete`), you must ensure proper implementation in **both backend and frontend**:

## Team

Tech Lead
@reqhiem