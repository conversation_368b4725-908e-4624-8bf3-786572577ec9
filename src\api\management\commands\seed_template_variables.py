from django.core.management.base import BaseCommand
from src.api.crm.services.seed.template_variable import populate_template_variable_data


class Command(BaseCommand):
    help = "Populate template variables from JSON assets using the new seed system"

    def add_arguments(self, parser):
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force execution even if data already exists",
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("Starting template variables seed..."))

        try:
            created_count, updated_count = populate_template_variable_data()

            self.stdout.write(
                self.style.SUCCESS(
                    f"Template variables seed completed successfully!\n"
                    f"Created: {created_count}\n"
                    f"Updated: {updated_count}"
                )
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error executing template variables seed: {str(e)}")
            )
            raise
