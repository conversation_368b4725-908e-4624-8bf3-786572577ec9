from django_filters import rest_framework as filters
from core.models import User as Contact
from django.db.models import Q


# TODO: This is not used yet, but it will be used in the future.
class CrmContactFilter(filters.FilterSet):
    """
    Filter for the Contact model.
    """

    is_staff = filters.<PERSON><PERSON>an<PERSON>ilter(field_name="is_staff")
    is_active = filters.BooleanFilter(field_name="is_active")

    search = filters.Char<PERSON>ilter(
        method="filter_search",
        help_text="Filter by first name, last name, email, or phone number.",
    )

    ocupation = filters.Char<PERSON>ilter(
        method="filter_ocupation",
        help_text="Filter by contact's occupation comma separated.",
    )

    major = filters.CharFilter(
        method="filter_major",
        help_text="Filter by contact's major comma separated.",
    )

    term = filters.CharFilter(
        method="filter_term",
        help_text="Filter by contact's term comma separated.",
    )

    educational_institution = filters.Char<PERSON>ilter(
        method="filter_educational_institution",
        help_text="Filter by contact's educational institution comma separated.",
    )

    # range filter created_at
    created_at = filters.DateFromToRangeFilter()

    def filter_ocupation(self, queryset, name, value):
        """
        Filter by contact's occupation. OR logic,
        if employe,student check if the user has employee or stu
        """
        if not value:
            return queryset

        ocupations = [v.strip() for v in value.split(",") if v.strip()]

        query = Q()
        for ocup in ocupations:
            query |= Q(ocupation__icontains=ocup)
        return queryset.filter(query)

    def filter_major(self, queryset, name, value):
        """
        Filter by contact's major. OR logic (FK by ID)
        """
        if not value:
            return queryset

        major_ids = [v.strip() for v in value.split(",") if v.strip()]
        return queryset.filter(major__in=major_ids)

    def filter_term(self, queryset, name, value):
        """
        Filter by contact's term. OR logic (FK by ID)
        """
        if not value:
            return queryset

        term_ids = [v.strip() for v in value.split(",") if v.strip()]
        return queryset.filter(term__in=term_ids)

    def filter_educational_institution(self, queryset, name, value):
        """
        Filter by contact's educational institution. OR logic (FK by ID)
        """
        if not value:
            return queryset

        educational_institution_ids = [v.strip() for v in value.split(",") if v.strip()]
        return queryset.filter(educational_institution__in=educational_institution_ids)

    def filter_search(self, queryset, name, value):
        """
        Filter by first name, last name, email, or phone number.
        """
        from django.db.models import Value, CharField
        from django.db.models.functions import Concat
        from api.crm.utils.phone_numbers import PhoneNumberUtils

        if not value:
            return queryset

        if PhoneNumberUtils.is_phone_number(value):
            # Use only the cleaned phone number for search
            value = PhoneNumberUtils.normalize_phone_number(value)["cleaned"]
            # filter only for phone number
            return queryset.filter(phone_number__icontains=value)

        queryset_with_full_name = queryset.annotate(
            full_name=Concat(
                "first_name",
                Value(" "),
                "last_name",
                output_field=CharField(),
            )
        )

        return queryset_with_full_name.filter(
            Q(first_name__icontains=value)
            | Q(last_name__icontains=value)
            | Q(email__icontains=value)
            | Q(phone_number__icontains=value)
            | Q(full_name__icontains=value)
        )

    class Meta:
        model = Contact
        fields = [
            "is_staff",
            "is_active",
            "created_at",
            "search",
            "ocupation",
            "major",
            "term",
        ]
