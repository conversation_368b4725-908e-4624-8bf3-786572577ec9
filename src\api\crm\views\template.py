from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from core.models.template import Template
from api.crm.serializers.template import (
    CrmCreateTemplateSerializer,
    CrmTemplateSerializer,
    CrmUpdateTemplateSerializer,
    CrmSendTemplateSerializer,
    CrmFetchTemplateStatusSerializer,
    CrmRetrieveTemplateSerializer,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import (
    IsAuthenticated,
    IsAdminUser,
    DjangoModelPermissions,
)
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.filters.template import CrmTemplateFilter
from api.crm.tasks import sync_whatsapp_templates
from api.crm.services.invitations.whatsapp import TokeChat
from services.cache.redis import CacheManager
from api.paginations import StandardResultsPagination


class CrmTemplateViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Template
    queryset = Template.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CrmTemplateSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser & DjangoModelPermissions]
    filterset_class = CrmTemplateFilter
    pagination_class = StandardResultsPagination
    swagger_tags = ["Templates"]

    # rate limiting for syncing templates
    cache_manager = CacheManager(prefix="templates", timeout=120)  # 2 minutes

    def get_serializer_class(self):
        if self.action == "create":
            return CrmCreateTemplateSerializer
        if self.action == "retrieve":
            return CrmRetrieveTemplateSerializer
        if self.action in ["update", "partial_update"]:
            return CrmUpdateTemplateSerializer

        return super().get_serializer_class()

    @action(detail=False, methods=["get"], url_path="approved")
    def list_approved(self, request):
        queryset = self.get_queryset().filter(status=Template.APPROVED)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["get"])
    def refresh_meta_status(self, request, pk=None):
        # Manual refresh endpoint
        instance = self.get_object()
        try:
            new_status = CrmFetchTemplateStatusSerializer().fetch_meta_template_status(
                instance
            )
            return Response({"status": new_status}, status=status.HTTP_200_OK)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"], url_path="send-to-meta")
    def send_to_meta(self, request, pk=None):
        """
        Acción personalizada para enviar una plantilla a Meta.
        """
        template = self.get_object()
        serializer = CrmSendTemplateSerializer(template)

        try:
            serializer.send_to_meta(template)
            return Response(
                {"detail": "Plantilla enviada a Meta con éxito."},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"detail": f"{str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(
        detail=False, methods=["post"], url_path="sync-templates", serializer_class=None
    )
    def sync_templates(self, request, pk=None):
        """
        Sincroniza las plantillas con la base de datos (síncrono).
        TODO: Modificar sincronización para verificar el estado de aprobación de plantillas si se requiere
        usar Meta API
        """
        # Usar cache manager como rate limiting para evitar múltiples ejecuciones simultáneas
        cache_key = "sync_templates_in_progress"
        info = self.cache_manager.debug_cache_info(cache_key)
        ttl_seconds = info.get("ttl_seconds", None)

        if self.cache_manager.get(cache_key):
            return Response(
                {
                    "detail": f" Inténtalo de nuevo en {ttl_seconds} segundos.",
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS,
            )

        tokechat = TokeChat()

        if not tokechat.is_available():
            return Response(
                {"detail": "TokeChat API no disponible"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        result = sync_whatsapp_templates()

        # si result es un error msg, retornar error, si empieza con Error syncing
        if result.startswith("Error"):
            return Response(
                {"detail": "Error al sincronizar las plantillas"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # set cache to True
        self.cache_manager.set(cache_key, True)

        return Response(
            {"detail": "Plantillas sincronizadas"},
            status=status.HTTP_200_OK,
        )

    def destroy(self, request, *args, **kwargs):
        """
        Marca una plantilla como eliminada en lugar de eliminarla físicamente.
        """
        instance = self.get_object()
        instance.deleted = True
        instance.save()

        return Response(
            {"detail": "La plantilla se marcó como eliminada."},
            status=status.HTTP_204_NO_CONTENT,
        )
