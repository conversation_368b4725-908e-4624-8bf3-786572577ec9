"""
Filters for Template (WhatsApp message) model
"""

import django_filters
from django.db.models import Q
from core.models import Template


class CrmTemplateFilter(django_filters.FilterSet):
    """Filter for Template with user search capabilities"""

    # Template name search
    search = django_filters.CharFilter(
        label="Template Name",
        field_name="name",
        lookup_expr="icontains",
    )

    status = django_filters.CharFilter(
        label="Status",
        field_name="status",
        lookup_expr="exact",
    )

    class Meta:
        model = Template
        fields = ["search", "status"]
