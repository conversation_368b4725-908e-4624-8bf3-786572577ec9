import uuid
from django.db import models
from django.contrib.contenttypes.models import ContentType
from core.models.base import AuditBaseModel


class TemplateType(AuditBaseModel):
    """
    Template Type model for mapping ContentTypes to user-friendly Spanish names
    """

    ttid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Nombre",
        help_text="Name in Spanish to display in the frontend (e.g., 'Recordatorio de eventos')",
    )
    content_type = models.OneToOneField(
        ContentType,
        on_delete=models.CASCADE,
        verbose_name="Modelo Asociado",
        help_text="Django model associated with this template type",
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name="Activo",
        help_text="If this template type is available to use",
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Message Template Type"
        verbose_name_plural = "Message Template Types"
        ordering = ["name"]
        indexes = [
            models.Index(fields=["is_active"]),
        ]


class TemplateVariable(AuditBaseModel):
    """
    Template Variable model for mapping user-friendly variable names to model paths
    """

    # Data types
    STRING = "string"
    DATETIME = "datetime"
    DATE = "date"
    INTEGER = "integer"
    BOOLEAN = "boolean"
    ARRAY = "array"
    DECIMAL = "decimal"

    DATA_TYPE_CHOICES = [
        (STRING, "String"),
        (DATETIME, "DateTime"),
        (DATE, "Date"),
        (INTEGER, "Integer"),
        (BOOLEAN, "Boolean"),
        (ARRAY, "Array"),
        (DECIMAL, "Decimal"),
    ]

    # Formats
    DATE_LONG = "date_long"
    DATE_SHORT = "date_short"
    DATE_WEEKDAY_MEDIUM = "date_weekday_medium"
    DATETIME_FULL = "datetime_full"
    DATETIME_SHORT = "datetime_short"
    TIME_12H = "time_12h"
    TIME_24H = "time_24h"
    CURRENCY = "currency"

    FORMAT_CHOICES = [
        (DATE_LONG, "Fecha larga (Viernes, 11 de julio de 2025)"),
        (DATE_SHORT, "Fecha corta (11/07/2025)"),
        (DATE_WEEKDAY_MEDIUM, "Fecha media (Viernes, 11 de julio)"),
        (TIME_12H, "Hora 12h (10:30 AM)"),
        (TIME_24H, "Hora 24h (22:30)"),
        (DATETIME_FULL, "Fecha y hora completa (Viernes, 11 de julio de 2025 a las 13:00)"),
        (DATETIME_SHORT, "Fecha y hora corta (11/07/2025 13:00)"),
        (CURRENCY, "Moneda"),
    ]
    
    tvid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Variable Name",
        help_text="Name used in templates (e.g., 'Nombre del contacto', 'Link del evento')",
    )
    path = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Model Path",
        help_text="Django model path to extract the value (e.g., 'enrollment__user__first_name')",
    )
    template_type = models.ForeignKey(
        TemplateType,
        on_delete=models.CASCADE,
        verbose_name="Tipo de Plantilla",
        help_text="Template type to which this variable belongs",
    )
    example = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="Example Value",
        help_text="Example value to show in template preview",
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name="Description",
        help_text="Description of what this variable represents",
    )

    data_type = models.CharField(
        max_length=20,
        choices=DATA_TYPE_CHOICES,
        default=STRING,
        verbose_name="Data Type",
        help_text="Type of data this variable represents",
    )

    data_format = models.CharField(
        max_length=30,
        choices=FORMAT_CHOICES,
        blank=True,
        null=True,
        verbose_name="Format",
        help_text="How to format the value when displaying",
    )

    def __str__(self):
        return f"{self.name} ({self.template_type.name})"

    class Meta:
        verbose_name = "Message Template Variable"
        verbose_name_plural = "Message Template Variables"
        unique_together = [["name", "template_type"]]
        indexes = [
            models.Index(fields=["template_type"]),
            models.Index(fields=["name"]),
            models.Index(fields=["data_type"]),
        ]


class Template(AuditBaseModel):
    DRAFT = "DRAFT"
    IN_REVIEW = "IN_REVIEW"
    REJECTED = "REJECTED"
    APPROVED = "APPROVED"
    PAUSED = "PAUSED"
    DISABLED = "DISABLED"

    STATUS_CHOICES = [
        (DRAFT, "Draft"),
        (IN_REVIEW, "In Review"),
        (REJECTED, "Rejected"),
        (APPROVED, "Approved"),
        (PAUSED, "Paused"),
        (DISABLED, "Disabled"),
    ]

    tid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    name = models.CharField(max_length=255, blank=False, verbose_name="Template Name")
    status = models.CharField(
        max_length=100,
        choices=STATUS_CHOICES,
        default=DRAFT,
        verbose_name="Status",
    )
    header_image = models.ForeignKey(
        "File",
        on_delete=models.SET_NULL,
        related_name="templates",
        blank=True,
        null=True,
        verbose_name="Header Image",
    )
    header_image_meta_url = models.TextField(
        blank=True, null=True, verbose_name="Header Image Meta URL"
    )
    body_text = models.TextField(
        blank=True, null=True, verbose_name="Body Text", max_length=1024
    )

    type = models.ForeignKey(
        TemplateType,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        verbose_name="Tipo de Plantilla",
        help_text="Template type that determines available variables",
    )

    positional_params_example = models.JSONField(
        blank=True, null=True, verbose_name="Positional Parameters Example"
    )

    buttons = models.JSONField(blank=True, null=True, verbose_name="Buttons")

    ext_reference = models.CharField(
        blank=True,
        null=True,
        verbose_name="External Reference for integrations, ej: flow ID's",
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Message Template"
        verbose_name_plural = "Message Templates"
