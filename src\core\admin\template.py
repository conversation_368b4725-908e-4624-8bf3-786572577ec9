from django.contrib import admin
from core.models.template import Template, TemplateVariable, TemplateType


@admin.register(Template)
class TemplateAdmin(admin.ModelAdmin):
    readonly_fields = [
        "created_by",
        "created_at",
        "updated_by",
        "updated_at",
        "deleted_by",
        "deleted_at",
    ]

    list_display = [
        "tid",
        "name",
        "status",
        "type",
        "created_at",
        "updated_at",
        "deleted",
    ]

    list_filter = (
        "status",
        "deleted",
        "type",
    )

    search_fields = ("name",)


@admin.register(TemplateVariable)
class TemplateVariableAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "path",
        "example",
        "description",
        "template_type",
    ]

    list_filter = ("template_type",)

    search_fields = ("name", "path")


@admin.register(TemplateType)
class TemplateTypeAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "content_type",
        "is_active",
    ]

    list_filter = ("is_active",)

    search_fields = ("name",)
