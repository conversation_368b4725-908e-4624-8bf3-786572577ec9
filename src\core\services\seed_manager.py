import logging
from typing import Callable, Dict, Any, Optional, <PERSON>ple
from django.db import transaction
from core.models import SeedExecution

logger = logging.getLogger(__name__)


class SeedManager:
    """
    Service to manage seed execution and prevent duplicate data population
    """
    
    @staticmethod
    def is_seed_executed(seed_name: str, version: str = "1.0.0") -> bool:
        """
        Check if a seed has already been executed successfully
        
        Args:
            seed_name: Unique identifier for the seed
            version: Version of the seed data
            
        Returns:
            True if seed was already executed successfully, False otherwise
        """
        return SeedExecution.objects.filter(
            seed_name=seed_name,
            version=version,
            success=True
        ).exists()
    
    @staticmethod
    def execute_seed(
        seed_name: str,
        populate_function: Callable[[], Tuple[int, int]],
        version: str = "1.0.0",
        force: bool = False
    ) -> Dict[str, Any]:
        """
        Execute a seed function if it hasn't been executed before
        
        Args:
            seed_name: Unique identifier for the seed
            populate_function: Function that returns (created_count, updated_count)
            version: Version of the seed data
            force: Force execution even if already executed
            
        Returns:
            Dictionary with execution results
        """
        if not force and SeedManager.is_seed_executed(seed_name, version):
            logger.info(f"Seed '{seed_name}' v{version} already executed, skipping...")
            return {
                "executed": False,
                "reason": "already_executed",
                "seed_name": seed_name,
                "version": version
            }
        
        logger.info(f"Executing seed '{seed_name}' v{version}...")
        
        try:
            with transaction.atomic():
                # Execute the populate function
                created_count, updated_count = populate_function()
                
                # Record successful execution
                SeedExecution.objects.update_or_create(
                    seed_name=seed_name,
                    defaults={
                        "version": version,
                        "success": True,
                        "error_message": None,
                        "records_created": created_count,
                        "records_updated": updated_count
                    }
                )
                
                logger.info(
                    f"Seed '{seed_name}' v{version} executed successfully. "
                    f"Created: {created_count}, Updated: {updated_count}"
                )
                
                return {
                    "executed": True,
                    "success": True,
                    "seed_name": seed_name,
                    "version": version,
                    "records_created": created_count,
                    "records_updated": updated_count
                }
                
        except Exception as e:
            error_message = str(e)
            logger.error(f"Seed '{seed_name}' v{version} failed: {error_message}")
            
            # Record failed execution
            SeedExecution.objects.update_or_create(
                seed_name=seed_name,
                defaults={
                    "version": version,
                    "success": False,
                    "error_message": error_message,
                    "records_created": 0,
                    "records_updated": 0
                }
            )
            
            return {
                "executed": True,
                "success": False,
                "seed_name": seed_name,
                "version": version,
                "error": error_message
            }
    
    @staticmethod
    def get_seed_status(seed_name: str, version: str = "1.0.0") -> Optional[SeedExecution]:
        """
        Get the execution status of a seed
        
        Args:
            seed_name: Unique identifier for the seed
            version: Version of the seed data
            
        Returns:
            SeedExecution instance or None if not found
        """
        try:
            return SeedExecution.objects.get(seed_name=seed_name, version=version)
        except SeedExecution.DoesNotExist:
            return None
    
    @staticmethod
    def list_executed_seeds() -> list:
        """
        List all executed seeds
        
        Returns:
            List of SeedExecution instances
        """
        return list(SeedExecution.objects.all().order_by("seed_name", "-executed_at"))
    
    @staticmethod
    def reset_seed(seed_name: str, version: str = "1.0.0") -> bool:
        """
        Reset a seed execution record (allows re-execution)
        
        Args:
            seed_name: Unique identifier for the seed
            version: Version of the seed data
            
        Returns:
            True if reset successfully, False if not found
        """
        try:
            seed_execution = SeedExecution.objects.get(
                seed_name=seed_name, 
                version=version
            )
            seed_execution.delete()
            logger.info(f"Reset seed '{seed_name}' v{version}")
            return True
        except SeedExecution.DoesNotExist:
            logger.warning(f"Seed '{seed_name}' v{version} not found for reset")
            return False
